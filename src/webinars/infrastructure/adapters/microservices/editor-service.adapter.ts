import { Injectable, Logger } from '@nestjs/common';
import { GrpcClient, GrpcInjectClient } from '@skillspace/grpc';

import {
    DocOwner,
    Document,
    IEditorServiceAdapter,
    RemoveDocResult,
    RemoveManyDocsResult,
} from '../../../application/adapters/microservices/editor-service-adapter.interface';

@Injectable()
export class EditorServiceAdapter implements IEditorServiceAdapter {
    private readonly logger = new Logger(EditorServiceAdapter.name);

    constructor(
        @GrpcInjectClient('EditorService')
        private readonly editorGrpc: GrpcClient<'EditorService'>,
    ) {}

    public async createDoc(owner: DocOwner): Promise<Document> {
        const docs = await this.createManyDocs([owner]);
        return docs[0];
    }

    public async removeDoc(docId: string): Promise<RemoveDocResult> {
        await this.removeManyDocs([docId]);
        return { success: true };
    }

    public async createManyDocs(owners: DocOwner[]): Promise<Document[]> {
        const data = await this.editorGrpc.send('CreateManyDocs', 'v1', {
            docs: owners.map((owner) => ({
                entityId: owner.streamId,
                schoolId: owner.schoolId,
                serviceId: 'webinars',
            })),
        });
        return data.docs.map(({ schoolId, entityId, docId }) => ({ streamId: entityId, schoolId, docId }));
    }

    public async removeManyDocs(docIds: string[]): Promise<RemoveManyDocsResult> {
        const result = await this.editorGrpc.send('RemoveManyDocs', 'v1', { docIds });
        if (!result.success) {
            return result;
        }
        return {
            success: true,
            removedDocIds: docIds,
        };
    }
}
