import { AggregateRoot } from '@nestjs/cqrs';
import { ParticipantRole, StreamType } from '@prisma/client';
import { CustomError, ERROR_CODE } from '@skillspace/utils';

import { StreamUpdatedEvent } from '../../../application/events/streams/stream-upated.event';
import { UploadedImageParams } from '../../objects/cover-image';
import { StreamId } from '../../objects/stream-id';
import { WebinarId } from '../../objects/webinar-id';
import { Speaker } from '../speaker';
import { WebinarUser } from '../user/user-abstract';
import { RoomParams, RoomStateEnum, StreamPlace, StreamPlaceParams } from './stream-room';

export const ALLOWED_TO_JOIN_AFTER_END = 60 * 60 * 1000;
export const ALLOWED_ADMIN_TO_JOIN_BEFORE_START = 60 * 60 * 1000;

export interface StreamParticipantParams {
    userUuid: string;
    role: ParticipantRole;
    email?: string;
}

export type StreamParams = StreamPlaceParams & {
    // TODO: countRegistered, countViewed
    schoolUuid: string;
    createdBy?: string;
    updatedAt?: Date;
    webinarId: string;
    streamType: StreamType;
    title: string;
    date: Date;
    duration: number;
    coverImage?: UploadedImageParams | null;
    participants: StreamParticipantParams[] | null; // тут только спикеры
    // record
    hasRecordUrl?: boolean;
    recordUrl?: string;
    // integrations
    autoRecord?: boolean;
    // calculated
    countRegistered?: number;
    countViewed?: number;
    // integrations
    kinescopeFolderId?: string;
    // doc
    docId?: string; // идентификатор документа
};

export interface StreamLogParams {
    title: string;
    date: string;
    type: string;
}

export type StreamCreateParams = Omit<
    StreamParams,
    'date' | 'participants' | 'hasRecordUrl' | 'recordUrl' | 'docId'
> & {
    date: number;
    speakers: string[];
};

export type StreamUpdateParams = Partial<Omit<StreamParams, 'date' | 'participants' | 'webinarId'>> & {
    date?: number;
    speakers?: string[];

    coverImage?: UploadedImageParams | null;
    hasRecordUrl?: boolean;
    recordUrl?: string;

    kinescopeFolderId?: string;
};

export class StreamModel extends AggregateRoot {
    public readonly id: string;
    public readonly kinescopeFolderId: string;
    public readonly title: string;
    public readonly room: RoomParams;
    public readonly webinarId: string;
    public readonly period: { start: Date; end: Date };
    public readonly docId: string;

    public readonly aWebinarId: WebinarId;
    public readonly speakers: Speaker[];
    public readonly students: StreamParticipantParams[];

    private readonly streamPlace: StreamPlace;

    public get embedRecordUrl(): string {
        return this.params.savedRecordUrl;
    }

    private constructor(
        public readonly anId: StreamId,
        private readonly _params: StreamParams,
    ) {
        super();
        this.aWebinarId = WebinarId.wrap(_params.webinarId);
        this.speakers = _params.participants
            .filter((participant) => participant.role === ParticipantRole.SPEAKER)
            .map((speaker) =>
                Speaker.from({
                    streamId: anId.unwrap(),
                    userUuid: speaker.userUuid,
                }),
            );
        this.students = _params.participants.filter((participant) => participant.role === ParticipantRole.STUDENT);
        Object.freeze(_params);

        this.id = anId.unwrap();
        this.title = _params.title;
        this.kinescopeFolderId = _params.kinescopeFolderId;
        this.docId = _params.docId || null;

        this.streamPlace = StreamPlace.from(_params);
        this.room = this.streamPlace.room;
        this.webinarId = _params.webinarId;

        this.period = {
            start: new Date(_params.date),
            end: new Date(new Date(_params.date).getTime() + _params.duration),
        };
    }

    static create(input: StreamCreateParams): StreamModel {
        const aStreamId = StreamId.generate();
        const createParams = {
            webinarId: input.webinarId,
            schoolUuid: input.schoolUuid,
            title: input.title,
            date: new Date(input.date),
            duration: input.duration,
            ...StreamPlace.create(input).params,
            participants: input.speakers.map((uuid) => ({
                userUuid: uuid,
                role: ParticipantRole.SPEAKER,
            })),
            kinescopeFolderId: input.kinescopeFolderId,
        };

        StreamModel.validate(createParams);
        return new StreamModel(aStreamId, createParams);
    }

    public update(input: StreamUpdateParams): StreamModel {
        const updateParams = {
            webinarId: this.params.webinarId,
            schoolUuid: this.params.schoolUuid,
            title: input.title || this.params.title,
            date: input?.date ? new Date(input.date) : this.params.date,
            duration: input.duration || this.params.duration,
            ...this.streamPlace.update(input).params,
            participants: input.speakers
                ? input.speakers.map((uuid) => ({
                      userUuid: uuid,
                      role: ParticipantRole.SPEAKER,
                  }))
                : this.params.participants,
            coverImage: input.coverImage || this.params.coverImage,
            hasRecordUrl: input.hasRecordUrl ?? this.params.hasRecordUrl,
            recordUrl: input.recordUrl !== undefined ? input.recordUrl : this.params.recordUrl,
            kinescopeFolderId: input.kinescopeFolderId || this.params.kinescopeFolderId,
        };

        StreamModel.validate(updateParams);
        const anUpdatedStream = new StreamModel(this.anId, updateParams);

        anUpdatedStream.apply(new StreamUpdatedEvent({ previous: this, updated: anUpdatedStream }));
        return anUpdatedStream;
    }

    public slotIsOver(currentDate = new Date()): boolean {
        return this.period.end < currentDate;
    }

    public streamingIsOver(currentDate = new Date()): boolean {
        if (this.isInternal) {
            return this.room.state === RoomStateEnum.closed;
        }
        const end = this.params.date.getTime() + this.params.duration;
        return end < currentDate.getTime() + ALLOWED_TO_JOIN_AFTER_END;
    }

    public get isInternal(): boolean {
        return this.params.streamType === StreamType.INTERNAL;
    }

    public isAllowedToJoin(anUser: WebinarUser, currentDate = new Date()): boolean {
        // Определяем временные границы для подключения
        const visitTime = currentDate.getTime();
        const justInTime = this.period.start.getTime();
        const oneHourBeforeStart = justInTime - ALLOWED_ADMIN_TO_JOIN_BEFORE_START;
        const oneHourAfterEnd = this.period.end.getTime() + ALLOWED_TO_JOIN_AFTER_END;

        if (this.isInternal) {
            const roomIsOpened = this.room.state !== RoomStateEnum.closed;

            if (anUser.isEmployee) {
                // Администраторы могут подключаться за 1 час до начала и пока открыта комната
                return oneHourBeforeStart <= visitTime && roomIsOpened;
            }

            // Все остальные могут подключаться только во время вебинара и пока открыта комната
            return justInTime <= visitTime && roomIsOpened;
        }

        if (anUser.isEmployee) {
            // Администраторы могут подключаться за 1 час до начала и до 1 часа после окончания
            return oneHourBeforeStart <= visitTime && visitTime <= oneHourAfterEnd;
        }

        // Все остальные могут подключаться только во время вебинара и до 1 часа после окончания
        return justInTime <= visitTime && visitTime <= oneHourAfterEnd;
    }

    static from(aStreamId: StreamId, streamParams: StreamParams): StreamModel {
        return new StreamModel(aStreamId, streamParams);
    }

    /**
     * Данные без учета студентов (только спикеры)
     */
    get params(): StreamParams {
        const { participants, ...rest } = this._params;
        return {
            ...rest,
            participants: participants.filter((participant) => participant.role === ParticipantRole.SPEAKER),
        };
    }

    get logParams(): StreamLogParams {
        return {
            title: this.params.title,
            date: this.params.date.toISOString(),
            type: this.isInternal ? 'внутренний' : 'внешний',
        };
    }

    public isRegisteredEmail(email: string): boolean {
        return this.students.some((student) => student.email === email);
    }

    static validate(input: StreamPlaceParams): void {
        if (input.autoRecord === true && input.streamType === StreamType.EXTERNAL) {
            throw new CustomError('Функция автоматической записи не доступна для внешнего потока', {
                code: ERROR_CODE.VALIDATION_ERROR,
                details: input,
            });
        }
    }
}
