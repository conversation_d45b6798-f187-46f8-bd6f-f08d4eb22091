import { Inject } from '@nestjs/common';
import { <PERSON>Handler, IEvent, IEventHandler } from '@nestjs/cqrs';

import { StreamModel } from '../../../domain/models/stream/stream.model';
import { WebinarModel } from '../../../domain/models/webinar/webinar.model';
import {
    EDITOR_SERVICE_ADAPTER,
    FILE_STORAGE_ADAPTER,
    STATISTICS_SERVICE_ADAPTER,
    VIDEO_STORAGE_ADAPTER,
} from '../../../injects';
import { IEditorServiceAdapter } from '../../adapters/microservices/editor-service-adapter.interface';
import { IStatisticsServiceAdapter } from '../../adapters/microservices/statistics-service-adapter.interface';
import { IFileStorageAdapter } from '../../adapters/saas/file-storage-adapter.interface';
import { IVideoStorageAdapter } from '../../adapters/saas/video-storage-adapter.interface';
import { PlansService } from '../../services/plans.service';
import { SchoolService } from '../../services/school.service';

export class StreamRemovedEvent implements IEvent {
    constructor(
        public readonly params: { aWebinar: WebinarModel; aStream: StreamModel },
        public readonly now = new Date(),
    ) {}
}

@EventsHandler(StreamRemovedEvent)
export class StreamRemovedEventHandler implements IEventHandler<StreamRemovedEvent> {
    constructor(
        @Inject(FILE_STORAGE_ADAPTER) private readonly storageService: IFileStorageAdapter,
        @Inject(VIDEO_STORAGE_ADAPTER) private readonly videoStorage: IVideoStorageAdapter,
        @Inject(STATISTICS_SERVICE_ADAPTER) private readonly statistics: IStatisticsServiceAdapter,
        @Inject(EDITOR_SERVICE_ADAPTER) private readonly contentService: IEditorServiceAdapter,
        private readonly schoolService: SchoolService,
        private readonly plansService: PlansService,
    ) {}
    async handle(event: StreamRemovedEvent): Promise<void> {
        const { aStream: aRemovedStream, aWebinar } = event.params;
        const { id: streamId, webinarId } = aRemovedStream;

        // Использование встроенных вебинаров
        if (aRemovedStream.isInternal && !aRemovedStream.streamingIsOver(event.now)) {
            await this.plansService.increaseQuota(aWebinar.params.schoolUuid);
        }

        // Статистика
        await this.statistics.notifyAboutRemovedStreams({
            removedStreamsIds: [aRemovedStream.id],
        });

        // Файлы
        await this.storageService.removeStreamCovers({ webinarId, streamId });

        // Удаление документов
        await this.contentService.removeManyDocs([aRemovedStream.params.docId]);

        // Хостинг видео
        const school = await this.schoolService.getSchool(aWebinar.schoolUuid);
        if (school) {
            await this.videoStorage.deleteStreamRecords(school, aRemovedStream.params);
        }
    }
}
