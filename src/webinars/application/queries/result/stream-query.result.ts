import { IQueryResult } from '@nestjs/cqrs';
import { ParticipantRole, StreamType } from '@prisma/client';

import { StreamParams } from '../../../domain/models/stream/stream.model';
import { RoomStateEnum } from '../../../domain/models/stream/stream-room';
import { WebinarUser } from '../../../domain/models/user/user-abstract';
import { WebinarModel } from '../../../domain/models/webinar/webinar.model';
import { CoverImage } from '../../../domain/objects/cover-image';
import { StudentsCount } from '../../../infrastructure/repositories/participant.repository';
import {
    mapToRegistrationTypePresentation,
    mapToStreamTypePresentation,
    RegistrationTypePresentation,
    StreamTypePresentation,
} from '../../../presentation/enum.presentation';

export class StreamQueryResult implements IQueryResult {
    id: string;
    // webinar
    webinarId: string;
    webinarTitle: string;
    webinarDescription: string;
    isPhoneRequiredOnRegistration: boolean;
    integrationCode: string;
    registrationType: RegistrationTypePresentation;
    // stream
    title: string;
    date: number;
    duration: number;
    streamType: StreamTypePresentation;
    externalStreamUrl: string;
    hasRecordUrl: boolean;
    recordUrl: string;
    autoRecord: boolean;
    savedRecordUrl: string;
    hasRecord: boolean;
    coverUrl: string;
    speakers: string[];
    countRegistered: number;
    countViewed: number;
    isRegistered: boolean;
    isSpeaker: boolean;
    canModify: boolean;
    canRemove: boolean;
    isAllowedToJoin: boolean;
    isLive: boolean;
    docId: string;

    constructor(
        anUser: WebinarUser,
        aWebinarParams: WebinarModel,
        stream: StreamParams & { id: string },
        studentsCount?: StudentsCount,
    ) {
        const userUuid = anUser.params?.id;
        const userEmail = anUser.params?.email;

        const speakers = stream.participants.filter((person) => person.role === ParticipantRole.SPEAKER);
        const students = stream.participants.filter((person) => person.role === ParticipantRole.STUDENT);

        const currentDate = Date.now();
        const startDate = new Date(stream.date).getTime();
        const isLive =
            stream.streamType === StreamType.INTERNAL
                ? stream.room.state === RoomStateEnum.started
                : startDate < currentDate && currentDate < startDate + stream.duration;

        this.id = stream.id;
        this.webinarDescription = aWebinarParams.params.description;
        this.title = stream.title;
        this.date = startDate; // WTF?
        this.duration = stream.duration;
        this.streamType = mapToStreamTypePresentation(stream.streamType);
        this.externalStreamUrl = stream.externalStreamUrl;
        this.coverUrl = CoverImage.from({ coverImage: stream.coverImage }).url;
        this.speakers = speakers.map((person) => person.userUuid);

        // records
        this.hasRecordUrl = stream.hasRecordUrl;
        this.recordUrl = stream.recordUrl;
        this.autoRecord = stream.autoRecord;
        this.savedRecordUrl = stream.savedRecordUrl;
        this.hasRecord = !!stream.recordUrl || !!stream.savedRecordUrl;

        // webinar
        this.webinarId = stream.webinarId;
        this.webinarTitle = aWebinarParams.params.title;
        this.integrationCode = aWebinarParams.params.integrationCode;
        this.registrationType = mapToRegistrationTypePresentation(aWebinarParams.params.registrationType);
        this.isPhoneRequiredOnRegistration = aWebinarParams.isPhoneRequiredOnRegistration;

        // permissions
        this.canModify = anUser.hasPermissionToModify(aWebinarParams);
        this.canRemove = anUser.hasPermissionToRemove(aWebinarParams);
        this.isAllowedToJoin = anUser.isAllowedToJoinStream(stream);

        // calculated
        this.countRegistered = studentsCount ? studentsCount.countRegistered : stream.countRegistered;
        this.countViewed = studentsCount ? studentsCount.countViewed : stream.countViewed;
        this.isRegistered = userEmail ? students.some((student) => student.email === userEmail) : false;
        this.isSpeaker = userUuid ? speakers.some((speaker) => speaker.userUuid === userUuid) : false;
        this.isLive = isLive;
        this.docId = stream.docId || null;
    }
}
