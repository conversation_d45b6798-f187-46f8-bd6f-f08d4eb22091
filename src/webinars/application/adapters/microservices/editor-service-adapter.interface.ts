export interface Document {
    schoolId: string;
    streamId: string;
    docId: string;
}

export interface DocOwner {
    schoolId: string;
    streamId: string;
}

export interface RemoveDocResult {
    success: boolean;
}

export interface RemoveManyDocsResult extends RemoveDocResult {
    removedDocIds: string[];
}

export interface IEditorServiceAdapter {
    createDoc(owner: DocOwner): Promise<Document>;
    removeDoc(docId: string): Promise<RemoveDocResult>;
    createManyDocs(owners: DocOwner[]): Promise<Document[]>;
    removeManyDocs(docIds: string[]): Promise<RemoveManyDocsResult>;
}
