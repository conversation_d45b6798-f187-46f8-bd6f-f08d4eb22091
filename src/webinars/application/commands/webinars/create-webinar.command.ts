import { Inject, Logger } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommand, ICommandHandler } from '@nestjs/cqrs';
import { StreamType } from '@prisma/client';

import { RoomParams } from '../../../domain/models/stream/stream-room';
import { WebinarUser } from '../../../domain/models/user/user-abstract';
import { WebinarCreateParams, WebinarModel } from '../../../domain/models/webinar/webinar.model';
import { WebinarId } from '../../../domain/objects/webinar-id';
import { IWebinarRepository } from '../../../domain/repositories/webinar-repository.interface';
import { EDITOR_SERVICE_ADAPTER, WEBINAR_PROVIDER_ADAPTER, WEBINAR_REPOSITORY } from '../../../injects';
import { IEditorServiceAdapter } from '../../adapters/microservices/editor-service-adapter.interface';
import { IWebinarProviderAdapter } from '../../adapters/saas/webinar-provider-adapter.interface';
import { PlansService } from '../../services/plans.service';

export class CreateWebinarCommand implements ICommand {
    constructor(
        public readonly anUser: WebinarUser,
        public readonly params: WebinarCreateParams,
    ) {}
}

@CommandHandler(CreateWebinarCommand)
export class CreateWebinarHandler implements ICommandHandler<CreateWebinarCommand> {
    constructor(
        private readonly publisher: EventPublisher,
        private readonly plansService: PlansService,
        @Inject(WEBINAR_REPOSITORY) private readonly webinarRepository: IWebinarRepository,
        @Inject(WEBINAR_PROVIDER_ADAPTER) private readonly webinarProvider: IWebinarProviderAdapter,
        @Inject(EDITOR_SERVICE_ADAPTER) private readonly contentService: IEditorServiceAdapter,
    ) {}

    private readonly logger = new Logger(CreateWebinarHandler.name);

    async execute(command: CreateWebinarCommand): Promise<string> {
        const { anUser, params } = command;

        const anInitialWebinar = WebinarModel.create(anUser, params);
        const aSchoolId = anInitialWebinar.aSchoolUuid;

        const streams = await Promise.all(
            anInitialWebinar.params.streams.map(async (stream) => {
                let room: RoomParams = null;
                if (stream.streamType === StreamType.INTERNAL) {
                    await this.plansService.checkInternalWebinarFeatureAccess(anUser.params.schoolUuid);
                    room = await this.webinarProvider.setupWebinarRoom(params.title, stream.autoRecord);
                }
                const doc = await this.contentService.createDoc({
                    schoolId: aSchoolId.unwrap(),
                    streamId: stream.id,
                });
                return room ? { ...stream, docId: doc.docId, room } : { ...stream, docId: doc.docId };
            }),
        );

        const aWebinar = WebinarModel.from(WebinarId.wrap(anInitialWebinar.id), {
            ...anInitialWebinar.params,
            streams,
        });
        this.publisher.mergeObjectContext(aWebinar);
        await this.webinarRepository.create(aWebinar);
        aWebinar.commit();
        return aWebinar.id;
    }
}
