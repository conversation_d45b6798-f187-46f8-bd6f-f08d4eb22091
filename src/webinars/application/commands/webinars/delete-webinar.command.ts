import { Inject, Logger } from '@nestjs/common';
import { CommandHandler, EventBus, ICommand, ICommandHandler } from '@nestjs/cqrs';

import { CustomError, ERROR_CODE } from '../../../../shared/webinar-service.error';
import { StreamModel } from '../../../domain/models/stream/stream.model';
import { WebinarUser } from '../../../domain/models/user/user-abstract';
import { StreamId } from '../../../domain/objects/stream-id';
import { WebinarId } from '../../../domain/objects/webinar-id';
import { IWebinarRepository } from '../../../domain/repositories/webinar-repository.interface';
import { WEBINAR_REPOSITORY } from '../../../injects';
import { StreamRemovedEvent } from '../../events/streams/stream-removed.event';
import { WebinarRemovedEvent } from '../../events/webinars/webinar-removed.event';

export class DeleteWebinarCommand implements ICommand {
    constructor(
        public readonly anUser: WebinarUser,
        public readonly aWebinarId: WebinarId,
    ) {}
}

@CommandHandler(DeleteWebinarCommand)
export class DeleteWebinarHandler implements ICommandHandler<DeleteWebinarCommand> {
    constructor(
        private readonly eventBus: EventBus,
        @Inject(WEBINAR_REPOSITORY)
        private readonly webinarRepository: IWebinarRepository,
    ) {}

    private readonly logger = new Logger(DeleteWebinarHandler.name);

    async execute(command: DeleteWebinarCommand) {
        const { anUser, aWebinarId } = command;
        const aWebinar = await this.webinarRepository.getWebinar(aWebinarId);

        if (!aWebinar) {
            throw new CustomError('Вебинар не найден', {
                code: ERROR_CODE.NOT_FOUND_ERROR,
                details: { webinarId: aWebinarId.unwrap() },
            });
        }

        if (!anUser.hasPermissionToRemove(aWebinar)) {
            throw new CustomError('Недостаточно прав для удаления вебинара', {
                code: ERROR_CODE.FORBIDDEN_ERROR,
                details: {
                    webinarId: aWebinarId.unwrap(),
                    user: anUser.toLog(),
                },
            });
        }

        await this.webinarRepository.delete(aWebinarId); // потоки и участники удаляются за счет настройки каскадного удаления

        this.eventBus.publish(new WebinarRemovedEvent(aWebinar));

        for (const stream of aWebinar.streams) {
            const { id: streamId, ...streamParams } = stream;
            this.eventBus.publish(
                new StreamRemovedEvent({ aWebinar, aStream: StreamModel.from(StreamId.wrap(streamId), streamParams) }),
            );
        }
    }
}
