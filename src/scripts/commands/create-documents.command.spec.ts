import { Test, TestingModule } from '@nestjs/testing';

import { OTEL_PRISMA_SERVICE } from '../../core/prisma/otel-prisma.service';
import { IEditorServiceAdapter } from '../../webinars/application/adapters/microservices/editor-service-adapter.interface';
import { EDITOR_SERVICE_ADAPTER } from '../../webinars/injects';
import { CreateDocumentsCommand } from './create-documents.command';

describe('CreateDocumentsCommand', () => {
    let command: CreateDocumentsCommand;
    let prismaService: any;
    let editorService: IEditorServiceAdapter;

    beforeEach(async () => {
        const mockPrismaService = {
            stream: {
                aggregateRaw: jest.fn(),
                update: jest.fn(),
            },
        };

        const mockEditorService: IEditorServiceAdapter = {
            createDoc: jest.fn(),
            removeDoc: jest.fn(),
            createManyDocs: jest.fn(),
            removeManyDocs: jest.fn(),
        };

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                CreateDocumentsCommand,
                {
                    provide: OTEL_PRISMA_SERVICE,
                    useValue: mockPrismaService,
                },
                {
                    provide: EDITOR_SERVICE_ADAPTER,
                    useValue: mockEditorService,
                },
            ],
        }).compile();

        command = module.get<CreateDocumentsCommand>(CreateDocumentsCommand);
        prismaService = module.get(OTEL_PRISMA_SERVICE);
        editorService = module.get<IEditorServiceAdapter>(EDITOR_SERVICE_ADAPTER);
    });

    it('should be defined', () => {
        expect(command).toBeDefined();
    });

    it('should process streams without docId', async () => {
        const mockStreams = [
            { id: 'stream1', schoolUuid: 'school1' },
            { id: 'stream2', schoolUuid: 'school2' },
        ];

        prismaService.stream.aggregateRaw.mockResolvedValue(mockStreams);
        (editorService.createDoc as jest.Mock).mockResolvedValue({ docId: 'doc123' });
        prismaService.stream.update.mockResolvedValue({});

        await command.run();

        expect(prismaService.stream.aggregateRaw).toHaveBeenCalledWith({
            pipeline: [
                {
                    $match: {
                        docId: { $exists: false },
                    },
                },
                {
                    $addFields: {
                        id: { $toString: '$_id' },
                    },
                },
                {
                    $project: {
                        _id: 1,
                        id: 1,
                        schoolUuid: 1,
                    },
                },
            ],
        });

        expect(editorService.createDoc).toHaveBeenCalledTimes(2);
        expect(prismaService.stream.update).toHaveBeenCalledTimes(2);
    });

    it('should handle errors and cleanup', async () => {
        const mockStreams = [{ id: 'stream1', schoolUuid: 'school1' }];

        prismaService.stream.aggregateRaw.mockResolvedValue(mockStreams);
        (editorService.createDoc as jest.Mock).mockResolvedValue({ docId: 'doc123' });
        prismaService.stream.update.mockRejectedValue(new Error('Update failed'));

        await command.run();

        expect(editorService.removeDoc).toHaveBeenCalledWith('doc123');
    });
});
