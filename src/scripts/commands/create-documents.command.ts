import { Inject, Logger } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { Command, CommandRunner } from 'nest-commander';

import { IEditorServiceAdapter } from '../../webinars/application/adapters/microservices/editor-service-adapter.interface';
import { EDITOR_SERVICE_ADAPTER } from '../../webinars/injects';

@Command({
    name: 'create-documents',
    description: 'Создает документы для потоков, у которых нет docId',
})
export class CreateDocumentsCommand extends CommandRunner {
    private readonly logger = new Logger(CreateDocumentsCommand.name);

    constructor(
        @Inject(EDITOR_SERVICE_ADAPTER)
        private readonly editorService: IEditorServiceAdapter,
    ) {
        super();
    }

    async run(): Promise<void> {
        this.logger.log('Начинаем создание документов для стримов без docId...');

        try {
            await this.createDocumentsForEmptyStreams();
            this.logger.log('Создание документов завершено успешно');
        } catch (error) {
            this.logger.error('Ошибка при создании документов:', error);
            throw error;
        }
    }

    private async createDocumentsForEmptyStreams() {
        const prisma = new PrismaClient();
        const streams = await prisma.stream.aggregateRaw({
            pipeline: [
                {
                    $match: {
                        docId: { $exists: false },
                    },
                },
                {
                    $addFields: {
                        id: { $toString: '$_id' },
                    },
                },
                {
                    $project: {
                        _id: 1,
                        id: 1,
                        schoolUuid: 1,
                    },
                },
            ],
        });

        console.log(streams);

        const streamEntries = streams as unknown as { id: string; schoolUuid: string }[];

        this.logger.log(`Найдено стримов для создания документов: ${streamEntries.length}`);

        for (const stream of streamEntries) {
            const docData = {
                schoolId: stream.schoolUuid,
                streamId: stream.id,
            };

            let doc: { docId: string } | undefined;

            try {
                // Создаем страницу
                // doc = await this.editorService.createDoc(docData);

                // // Обновляем поток
                // await prisma.stream.update({
                //     where: { id: stream.id },
                //     data: {
                //         docId: doc.docId,
                //     },
                // });

                this.logger.log(`Создан документ ${doc.docId} для стрима ${stream.id}`);
            } catch (error) {
                this.logger.error({
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access
                    message: error.message,
                    docData,
                });

                if (doc?.docId) {
                    try {
                        await this.editorService.removeDoc(doc.docId);
                        this.logger.log(`Удален некорректный документ ${doc.docId}`);
                    } catch (removeError) {
                        this.logger.error(`Ошибка при удалении документа ${doc.docId}:`, removeError);
                    }
                }
            }
        }
    }
}
