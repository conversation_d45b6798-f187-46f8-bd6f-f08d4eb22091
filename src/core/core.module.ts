import { Global, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_GUARD, Reflector } from '@nestjs/core';
import { CqrsModule } from '@nestjs/cqrs';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { AuthModule, JwtAuthGqlGuard, PermissionsGuard } from '@skillspace/access';
import {
    AmqpBrokerModule,
    AmqpExceptionFilter,
    AmqpInterceptor,
    EMAIL_SERVICE_EXCHANGE,
    MONOLITH_EXCHANGE,
    STATISTICS_EXCHANGE,
    WEBINARS_EXCHANGE,
} from '@skillspace/amqp-contracts';
import { ExecutionContextRouterModule, HealthModule } from '@skillspace/common';
import {
    ApolloFederationDriver,
    ApolloFederationDriverConfig,
    ApolloServerPluginInlineTraceDisabled,
    GqlInterceptor,
    GraphqlExceptionFilter,
    GraphQLModule,
} from '@skillspace/graphql';
import { GrpcClientModule } from '@skillspace/grpc';
import { LoggerModule } from '@skillspace/logger';
import { OpentelemetryModule } from '@skillspace/tracing';

import { appConfig, NODE_ENV } from '../configs/app.config';
import { PrismaModule } from './prisma/prisma.module';
import { PrismaService } from './prisma/prisma.service';

const AMQP_CONFIG_EXCHANGES = [WEBINARS_EXCHANGE, MONOLITH_EXCHANGE, STATISTICS_EXCHANGE, EMAIL_SERVICE_EXCHANGE];

@Global()
@Module({
    imports: [
        ConfigModule.forRoot({
            isGlobal: true,
            load: [() => ({ app: appConfig })],
        }),
        EventEmitterModule.forRoot(),
        CqrsModule,
        ScheduleModule.forRoot(),
        AuthModule.registerAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => ({
                global: true,
                secret: configService.get<string>('JWT_SECRET'),
            }),
        }),
        GraphQLModule.forRoot<ApolloFederationDriverConfig>({
            driver: ApolloFederationDriver,
            playground: appConfig.nodeEnv !== NODE_ENV.PROD,
            autoSchemaFile: true,
            formatError: (error) => {
                return {
                    message: error.message,
                    path: error.path,
                };
            },
            plugins: appConfig.nodeEnv === NODE_ENV.TEST ? [ApolloServerPluginInlineTraceDisabled()] : [],
        }),
        LoggerModule.forRoot(),
        AmqpBrokerModule.forRootAsync({
            imports: [ConfigModule],
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => {
                return {
                    exchanges: AMQP_CONFIG_EXCHANGES,
                    mainExchange: WEBINARS_EXCHANGE,
                    uri: configService.get<string>('RMQ_URL'),
                };
            },
        }),
        HealthModule.register({
            prisma: PrismaService,
        }),
        PrismaModule,
        OpentelemetryModule.forRoot(),
        ExecutionContextRouterModule.register({
            gqlInterceptor: GqlInterceptor,
            amqpInterceptor: AmqpInterceptor,
            gqlFilter: GraphqlExceptionFilter,
            amqpFilter: AmqpExceptionFilter,
        }),
        GrpcClientModule.registerAsync({
            serviceName: 'MonolithService',
            useFactory: () => appConfig.monolithGrpcAddress,
        }),
        GrpcClientModule.registerAsync({
            serviceName: 'EditorService',
            useFactory: () => appConfig.editorGrpcAddress,
        }),
    ],
    providers: [
        {
            provide: APP_GUARD,
            useClass: JwtAuthGqlGuard,
        },
        {
            provide: APP_GUARD,
            useClass: PermissionsGuard,
        },
        Reflector,
    ],
    exports: [AuthModule, PrismaModule, AmqpBrokerModule, CqrsModule, GrpcClientModule],
})
export class CoreModule {}
