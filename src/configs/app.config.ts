import { SaasProviderEnum } from '@prisma/client';

import { resolveInt, resolveStr } from '../shared/utils/environment.utils';

export const DEFAULT_VIDEO_STORAGE = SaasProviderEnum.Kinescope;
export const DEFAULT_WEBINAR_PROVIDER = SaasProviderEnum.LiveDigital;

export const EXTERNAL_API_RETRY_COUNT = 3;
export const EXTERNAL_API_RETRY_DELAY = 500;

export const NODE_ENV = {
    DEV: 'development',
    PROD: 'production',
    TEST: 'test',
} as const;

export interface IAppConfig {
    port: number;
    nodeEnv: string;
    jwtSecret: string;
    monolithGrpcAddress: string;
    editorGrpcAddress: string;
}

const nodeEnv = resolveStr('NODE_ENV', process.env.NODE_ENV);

export const isTest = nodeEnv === NODE_ENV.TEST;
export const WAIT_FOR_USER_ID_MS = 10_000;

export const appConfig: IAppConfig = {
    port: resolveInt('APP_PORT', process.env.APP_PORT, {
        default: 3000,
    }),
    nodeEnv,
    jwtSecret: resolveStr('JWT_SECRET', process.env.JWT_SECRET),
    monolithGrpcAddress: resolveStr('MONOLITH_GRPC_ADDRESS', process.env.MONOLITH_GRPC_ADDRESS),
    editorGrpcAddress: resolveStr('EDITOR_GRPC_ADDRESS', process.env.EDITOR_GRPC_ADDRESS),
};
