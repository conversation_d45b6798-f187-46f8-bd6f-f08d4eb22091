module.exports = {
    /**
     * @param db {import('mongodb').Db}
     * @param client {import('mongodb').MongoClient}
     * @returns {Promise<void>}
     */
    async up(db, client) {
        const streamsCollection = db.collection('streams');
        const participantsCollection = db.collection('participants');

        // Находим все stream.webinarId, для которых нет webinar в коллекции
        const danglingStreams = await streamsCollection
            .aggregate([
                {
                    $lookup: {
                        from: 'webinars',
                        localField: 'webinarId',
                        foreignField: '_id',
                        as: 'webinar',
                    },
                },
                {
                    $addFields: {
                        webinarId: { $toString: '$webinarId' },
                    },
                },
                {
                    $match: {
                        'webinar.0': { $exists: false }, // webinar не найден
                    },
                },
                {
                    $project: {
                        _id: 1,
                        webinarId: 1,
                    },
                },
            ])
            .toArray();

        const total = danglingStreams.length;
        console.log(`Найдено ${total} потоков без соответствующего вебинара`);

        if (total === 0) {
            return;
        }

        const danglingStreamIds = danglingStreams.map((s) => s._id);

        // Удаляем участников, связанных с этими потоками
        const deletedParticipants = await participantsCollection.deleteMany({
            streamId: { $in: danglingStreamIds },
        });

        console.log(`Удалено ${deletedParticipants.deletedCount} участников, связанных с висячими потоками`);

        // Удаляем сами потоки
        const deletedStreams = await streamsCollection.deleteMany({
            _id: { $in: danglingStreamIds },
        });

        console.log(`Удалено ${deletedStreams.deletedCount} висячих потоков`);
    },

    /**
     * Откатывать такую миграцию нельзя — данные уже удалены.
     * Можно оставить пустую функцию или выбросить ошибку.
     *
     * @param db {import('mongodb').Db}
     * @param client {import('mongodb').MongoClient}
     * @returns {Promise<void>}
     */
    async down(db, client) {
        console.warn('Откат невозможен: данные были удалены');
    },
};
