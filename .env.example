NODE_ENV=development

APP_PORT=3010
DB_PORT=5010

RMQ_URL=amqp://rabbitmq:5672

DATABASE_URL=mongodb://webinars-service-db:27017/webinars?directConnection=true
DATABASE_DEV_MIGRATION_URL=mongodb://localhost:5010/webinars?directConnection=true

JWT_SECRET=know_your_secret

MONOLITH_GRPC_ADDRESS=*************:9700 # local: 0.0.0.0:50051
EDITOR_GRPC_ADDRESS=*************:50055 # local: 0.0.0.0:50051

# S3
S3_REGION=ru-central1
S3_ENDPOINT=https://storage.yandexcloud.net
S3_BUCKET= webinars-s3
S3_BUCKET_URL=https://storage.yandexcloud.net
S3_API_KEY=know_your_key
S3_SECRET_KEY=know_your_secret

# Live Digital
LIVE_DIGITAL_API_URL=https://moodhood-api.livedigital.space
LIVE_DIGITAL_ACCESS_TOKEN=know_your_token
LIVE_DIGITAL_SPACE_ID=67c8100b5b029103e180b993
LIVE_DIGITAL_WEBHOOK_URL=http://*************:3010/s/webinars/webhooks/live-digital

# Kinescope
KINESCOPE_API_URL=https://api.kinescope.io
KINESCOPE_ACCESS_TOKEN=know_your_token

# Captcha
CAPTCHA_SECRET=know_your_secret

# Opentelemetry
OTEL_SERVICE_NAME=webinars-DEV

