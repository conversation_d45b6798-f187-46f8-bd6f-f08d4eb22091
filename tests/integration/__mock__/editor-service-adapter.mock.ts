import { setTimeout } from 'node:timers/promises';

import {
    DocOwner,
    Document,
    IEditorServiceAdapter,
    RemoveDocResult,
    RemoveManyDocsResult,
} from '../../../src/webinars/application/adapters/microservices/editor-service-adapter.interface';

export class EditorServiceAdapterMock implements IEditorServiceAdapter {
    public async createDoc(owner: DocOwner): Promise<Document> {
        await setTimeout();
        return { docId: '123', ...owner };
    }

    public async removeDoc(docId: string): Promise<RemoveDocResult> {
        await setTimeout();
        void docId;
        return { success: true };
    }

    public async createManyDocs(owners: DocOwner[]): Promise<Document[]> {
        await setTimeout();
        return owners.map((owner) => ({ docId: '123', ...owner }));
    }

    public async removeManyDocs(docIds: string[]): Promise<RemoveManyDocsResult> {
        await setTimeout();
        return {
            success: true,
            removedDocIds: docIds,
        };
    }
}
