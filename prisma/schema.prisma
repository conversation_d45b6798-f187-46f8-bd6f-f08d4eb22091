generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl", "linux-musl-arm64-openssl-1.1.x", "linux-musl-arm64-openssl-3.0.x", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider     = "mongodb"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

type KinescopeSchoolMeta {
  projectId      String
  folderId       String
}

model School {
  id String @id @default(auto()) @map("_id") @db.ObjectId
  uuid  String
  kinescope KinescopeSchoolMeta

  @@unique([uuid])
}

enum WebinarVisibility {
  DRAFT
  PUBLIC
  PRIVATE
}

enum WebinarType {
  INTERNAL
  EXTERNAL
}

enum RegistrationType {
  LANDING
  INVITATION
}

type coverImage {
  key  String
  name String
}

model Webinar {
  id String @id @default(auto()) @map("_id") @db.ObjectId
  kinescopeFolderId String?

  // Meta
  type       WebinarType
  schoolUuid String
  createdBy  String
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt

  // Base
  title       String
  description String
  coverImage  coverImage?

  // Attendance Points
  useAttendancePoints Boolean
  attendancePoints    Int

  // Notifications
  useNotifications Boolean @default(true)
  notifications    Int[]

  // Access
  visibility         WebinarVisibility @default(DRAFT)
  enableStudentLimit Boolean
  maxStudentCapacity Int

  // Registration
  registrationType     RegistrationType
  registrationForEntry Boolean
  registrationForView  Boolean
  isPhoneRequiredOnRegistration Boolean @default(false)

  // Landing
  useDefaultLanding  Boolean @default(false)
  externalLandingUrl String?

  integrationCode String?

  // Relations
  streams Stream[]

  @@index([kinescopeFolderId])
  @@index([schoolUuid])
  @@map("webinars")
}

enum StreamType {
  INTERNAL
  EXTERNAL
}

enum SaasProviderEnum {
  LiveDigital
  Kinescope
}

type ProviderRoom {
  state       String?
  // provider
  provider SaasProviderEnum
  spaceId     String
  roomId      String
  uploadedRecordIds String[] @default([])
  // storage
  storage     SaasProviderEnum
}

enum StorageStateEnum {
  uploading
  processing
  finished
}

model Stream {
  id String @id @default(auto()) @map("_id") @db.ObjectId
  kinescopeFolderId String?

  // Meta
  schoolUuid String?
  webinarId String   @db.ObjectId
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Info
  title             String
  date              DateTime
  coverImage        coverImage?
  duration          Int
  streamType        StreamType
  externalStreamUrl String?

  hasRecordUrl Boolean @default(false)
  recordUrl    String?

  // Internal webinar
  room            ProviderRoom?
  autoRecord      Boolean @default(false)
  savedRecordUrl  String?
  storageState    StorageStateEnum? 

  docId String?

  // Relations
  webinar      Webinar       @relation(fields: [webinarId], references: [id], onDelete: Cascade)
  participants Participant[]

  // @@index([schoolUuid])
  @@index([kinescopeFolderId])
  @@index([webinarId])
  @@map("streams")
}

enum StatisticRecordType {
  INVITED
  REGISTERED
  ATTENDED
  REPLAYED
}

enum ParticipantRole {
  STUDENT
  SPEAKER
}

type StreamStatisticRecord {
  type  StatisticRecordType
  date  DateTime
  score Int                 @default(0)
}

// TODO: перенести спикеров отсюда в потоки
// Назвать эту коллекцию Statistics
model Participant {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  // student info
  email    String?
  name     String?
  timezone String?
  phoneNumber String?
  userUuid String? // студенты получают после синхронизации

  role          ParticipantRole      @default(STUDENT)
  currentStatus StatisticRecordType? // для студентов

  streamId String @db.ObjectId

  // TODO: вот это может и не нужно - все есть в statistics
  isViewed         Boolean @default(false)
  attendancePoints Int     @default(0)

  statistics StreamStatisticRecord[]

  // Relations
  stream Stream @relation(fields: [streamId], references: [id], onDelete: Cascade)

  @@index([userUuid])
  @@map("participants")
}

